import React, { FC, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'

import {
  useDeleteEmployeesMutation,
  useExportEmployeesMutation,
  useReinviteEmployeesMutation,
} from '@/store/services/tags-employees-service'
import { EmployessExportModal } from '@/pages/admin/employess-export-modal'
import { useEmployees } from '@/entities/employee'
import EmailCloseMediumIcon from '@/shared/ui/Icon/icons/components/EmailCloseMediumIcon'
import FileIcon from '@/shared/ui/Icon/icons/components/FileIcon'
import EmailCloseBoldIcon from '@/shared/ui/Icon/icons/components/EmailCloseBoldIcon'
import TrashBoldIcon from '@/shared/ui/Icon/icons/components/TrashBoldIcon'
import FailIcon from '@/shared/ui/Icon/icons/components/FailIcon'
import HatGraduationIcon from '@/shared/ui/Icon/icons/components/HatGraduationIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { DeleteModal } from '@/shared/modals/delete-modal'
import { URLS } from '@/shared/configs/urls'

import styles from './employees-panel.module.scss'
import { EmployeesPanelProps } from './employees-panel.d'

const cx = classNamesBind.bind(styles)

export const EmployeesPanel: FC<EmployeesPanelProps.Props> = props => {
  const { className } = props

  const {
    checkedEmployees,
    setCheckedEmployees,
    isAllSelected,
    setIsAllSelected,
    excludeEmployees,
    setExcludeEmployees,
    debouncedSearch,
    filter,
    setUseInPhishingCampaign,
    setUseInCourseAssignment,
    getCountOfEmployess,
  } = useEmployees()
  const navigate = useNavigate()

  const {
    departments,
    phishingEvents,
    role,
    courses,
    courseProgress,
    phishing,
    learning,
    tags,
    riskLevelMax,
    riskLevelMin,
  } = filter || {}

  const [deleteEmployees, { isLoading: isDeleteLoading }] = useDeleteEmployeesMutation()
  const [reinviteEmployees, { isLoading: isReinviteLoading }] = useReinviteEmployeesMutation()
  const [generateReport, { data: generateReportData, reset, isError, isLoading: isReportLoading }] =
    useExportEmployeesMutation()

  const handleReset = () => {
    setCheckedEmployees([])
    setExcludeEmployees([])
    setIsAllSelected(false)
  }

  const { t } = useTranslation()

  const [deletedIDs, setDeletedIDs] = useState<UUID[] | null>(null)
  const [openDeleteModal, setOpenDeleteModal] = useState(false)
  const [isExportModalOpen, setIsExportModalOpen] = useState(false)

  const onDeleteClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, IDs: UUID[]) => {
    e.stopPropagation()

    setDeletedIDs(IDs)
    setOpenDeleteModal(true)
  }

  const handleDeleteEmployees = async (IDs?: UUID[] | null) => {
    if ((IDs && !!IDs.length) || isAllSelected) {
      if (!IDs) return

      const ids = isAllSelected
        ? {
            exclude_ids: IDs,
          }
        : {
            include_ids: IDs,
          }

      await deleteEmployees({
        ids,
        all: isAllSelected,
        filter: {
          search: debouncedSearch,
          departments: departments,
          phishing_events: phishingEvents,
          role: role,
          courses: courses,
          courseProgress: courseProgress,
          phishing: phishing,
          learning: learning,
          tags: tags,
          riskLevelMax: riskLevelMax,
          riskLevelMin: riskLevelMin,
        },
      })
        .unwrap()
        .then(() => {
          setCheckedEmployees([])
          setExcludeEmployees([])
          setIsAllSelected(false)
        })
    }

    setDeletedIDs(null)
    setOpenDeleteModal(false)
  }

  const onReinviteClick = async (e: React.MouseEvent<HTMLDivElement, MouseEvent>, all: boolean) => {
    e.stopPropagation()
    const IDs = all ? excludeEmployees : checkedEmployees

    if (!IDs.length && !all) return
    const ids = all
      ? {
          exclude_ids: IDs,
        }
      : {
          include_ids: IDs,
        }

    await reinviteEmployees({
      ids,
      all,
      filter: {
        search: debouncedSearch,
        departments: departments,
        phishing_events: phishingEvents,
        role: role,
        courses: courses,
        courseProgress: courseProgress,
        phishing: phishing,
        learning: learning,
        tags: tags,
        riskLevelMax: riskLevelMax,
        riskLevelMin: riskLevelMin,
      },
    })
      .unwrap()
      .then(() => {
        setCheckedEmployees([])
        setExcludeEmployees([])
        setIsAllSelected(false)
      })
  }

  const handleGenerateReport = async () => {
    const IDs = isAllSelected ? excludeEmployees : checkedEmployees

    if (!IDs.length && !isAllSelected) return
    const ids = isAllSelected
      ? {
          exclude_ids: IDs,
        }
      : {
          include_ids: IDs,
        }

    await generateReport({
      ids,
      all: isAllSelected,
      filter: {
        search: debouncedSearch,
        departments: departments,
        phishing_events: phishingEvents,
        role: role,
        courses: courses,
        courseProgress: courseProgress,
        phishing: phishing,
        learning: learning,
        tags: tags,
        riskLevelMax: riskLevelMax,
        riskLevelMin: riskLevelMin,
      },
    })
      .unwrap()
      .then(() => {
        setCheckedEmployees([])
        setExcludeEmployees([])
        setIsAllSelected(false)
      })
  }

  if (!checkedEmployees.length && !isAllSelected && !isExportModalOpen) return <></>

  return (
    <>
      <div className={cx('wrapper', className)}>
        <div className={cx('counter')}>
          {t('commons:employees_choosed', { count: getCountOfEmployess() })}
        </div>
        <div className={cx('buttons')}>
          <div
            className={cx('button', {
              disabled: isReportLoading,
            })}
            onClick={() => {
              setUseInCourseAssignment(true)
              navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_CREATE_PAGE)
            }}
          >
            <IconWrapper>
              <HatGraduationIcon />
            </IconWrapper>
            {t('commons:assign_course')}
          </div>
          <div
            className={cx('button', {
              disabled: isReportLoading,
            })}
            onClick={() => {
              setUseInPhishingCampaign(true)
              navigate(URLS.ADMIN_PHISHING_CAMPAIGNS_CREATE_PAGE)
            }}
          >
            <IconWrapper>
              <EmailCloseMediumIcon />
            </IconWrapper>
            {t('commons:create_mailing')}
          </div>
          <div
            className={cx('button', {
              disabled: isReportLoading,
            })}
            onClick={() => setIsExportModalOpen(true)}
          >
            <IconWrapper>
              <FileIcon />
            </IconWrapper>
            {t('commons:upload_list')}
          </div>
          <div
            className={cx('button', {
              disabled: isReinviteLoading,
            })}
            onClick={e => onReinviteClick(e, isAllSelected)}
          >
            <IconWrapper>
              <EmailCloseBoldIcon />
            </IconWrapper>
            {t('commons:send_registration_letter')}
          </div>
          <div
            className={cx('button', 'delete', {
              disabled: isDeleteLoading,
            })}
            onClick={e => onDeleteClick(e, isAllSelected ? excludeEmployees : checkedEmployees)}
          >
            <IconWrapper>
              <TrashBoldIcon />
            </IconWrapper>
            {t('commons:remove_employees')}
          </div>
          <div className={cx('cancel')} onClick={handleReset}>
            <IconWrapper>
              <FailIcon />
            </IconWrapper>
            {t('commons:cancel')}
          </div>
        </div>
      </div>

      {isExportModalOpen && (
        <EmployessExportModal
          open={isExportModalOpen}
          setOpen={setIsExportModalOpen}
          generateReport={handleGenerateReport}
          reset={reset}
          isError={isError}
          data={generateReportData}
          isReportLoading={isReportLoading}
        />
      )}

      {deletedIDs && openDeleteModal && (
        <DeleteModal
          ids={deletedIDs}
          onCloseIDs={handleDeleteEmployees}
          title=''
          active={openDeleteModal}
          setActive={setOpenDeleteModal}
        />
      )}
    </>
  )
}
