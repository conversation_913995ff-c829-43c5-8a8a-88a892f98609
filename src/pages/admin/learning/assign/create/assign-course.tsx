import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Checkbox, Input, Textarea, Tooltip } from '@/shared/ui'
import RiskLevelCircleIcon from '@/shared/ui/Icon/icons/components/RiskLevelCircleIcon'
import EditBoldIcon from '@/shared/ui/Icon/icons/components/EditBoldIcon'
import TrashBoldIcon from '@/shared/ui/Icon/icons/components/TrashBoldIcon'
import ArticleWarningIcon from '@/shared/ui/Icon/icons/components/ArticleWarningIcon'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import styles from './assign-course.module.scss'
import classNamesBind from 'classnames/bind'
import { useEffect, useMemo, useState } from 'react'
import { URLS } from '@/shared/configs/urls'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { SelectTargetCard } from '@/shared/components'
import { isNumber } from '@/shared/helpers'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  OrganizationTree,
  OrganizationTreeProvider,
  useUnmountOrganizationTree,
  departmentsTreeSlice,
} from '@/shared/modals/organization-tree'
import type { UserNode } from '@/shared/modals/organization-tree/types'
import {
  onChangeSelectedCourseById,
  onDeleteFromSelectedCourses,
  resetFullStateForm,
  selectForm,
  selectSelectedCourses,
  selectTargetModalOpen,
  selectActualSelectedCountOfPeople,
  setForm,
  setSelectedCountOfPeople,
  setTargetModalOpen,
  setTargets,
} from './slice'
import { v4 as uuid } from 'uuid'
import { CourseCard } from './course-card'
import { useNavigate } from 'react-router-dom'
import { zodResolver } from '@hookform/resolvers/zod'
import { getAssignCourseSchema } from './resolver'
import {
  addDays,
  DASH_DATE_FORMAT,
  differenceInDays,
  endOfDay,
  format,
  startOfDay,
} from '@/shared/helpers/date'
import { useNotification } from '@/shared/contexts/notifications'
import { CourseForAssign, coursesApi, Targets } from '@/entities/courses'
import ImageUpload from '@/shared/components/image-upload'
import { SelectDateCardWithInput } from '@/shared/components/SelectDateCard/select-date-card-with-input'
import { useEmployees } from '@/entities/employee'

const cx = classNamesBind.bind(styles)

export type FormValues = {
  date_start: Date | null
  date_end: Date | null
  notifications: {
    lagging: boolean
    assign: boolean
  }
  image?: File | string
  courses: CourseForAssign[]
  targets: Targets
}

export const Create = () => {
  const { t } = useTranslation('pages__learning__assign-course')
  const dispatch = useAppDispatch()
  const targetModalOpen = useAppSelector(selectTargetModalOpen)
  const newCountOfEmployes = useAppSelector(selectActualSelectedCountOfPeople)
  const navigate = useNavigate()
  const selectedCourses = useAppSelector(selectSelectedCourses)
  const [assignCourses, { isLoading: isAssignCoursesLoading }] =
    coursesApi.useAssignCoursesMutation()
  const [assignCourse, { isLoading: isAssignCourseLoading }] = coursesApi.useAssignCourseMutation()
  const [attachPicture, { isLoading: isAttachCourseLoading }] =
    coursesApi.useAttachPictureToCourseMutation()
  const storeForm = useAppSelector(selectForm)
  const { add } = useNotification()

  const {
    checkedEmployees,
    excludeEmployees,
    isAllSelected,
    useInCourseAssignment,
    setUseInCourseAssignment,
    filter,
    debouncedSearch,
    getCountOfEmployess,
    hasFilter,
    employees,
  } = useEmployees()
  const schema = useMemo(
    () =>
      getAssignCourseSchema({
        coursesRequiredMsg: t('validation.courses_required'),
        endDateReq: t('validation.end_date_required'),
        startDateReq: t('validation.start_date_required'),
        targetsReq: t('validation.targets_required'),
        startDateGreaterThanEndDateMsg: t('validation.start_date_gt_end'),
      }),
    [t],
  )
  const isLoading = isAssignCoursesLoading || isAssignCourseLoading || isAttachCourseLoading
  const [editingCourse, setEditingCourse] = useState<string>()
  useUnmountOrganizationTree()

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: storeForm,
  })

  useEffect(() => {
    form.setValue(
      'courses',
      selectedCourses?.map(c => ({
        course_id: c.id,
        title: c.title,
        description: c.description || '',
      })),
      {
        shouldValidate: true,
      },
    )
  }, [form, selectedCourses])

  useEffect(() => {
    if (!useInCourseAssignment) return

    const targets: Targets = {
      users: isAllSelected ? [] : checkedEmployees,
      departments: [],
      exclude_users_ids: isAllSelected ? excludeEmployees : [],
    }

    form.setValue('targets', targets, { shouldValidate: true })

    dispatch(
      setTargets({
        ...targets,
        isAllSelected,
      }),
    )

    dispatch(setSelectedCountOfPeople(getCountOfEmployess()))

    if (isAllSelected) {
      dispatch(departmentsTreeSlice.actions.setSelectAll(true))
    } else if (checkedEmployees.length > 0 && employees?.data) {
      const checkedUsersObj = checkedEmployees.reduce(
        (acc, userId) => {
          const user = employees.data.find(u => u.id === userId)
          if (user) {
            const userNode: UserNode = {
              id: user.id,
              key: user.id,
              title: `${user.first_name || ''} ${user.last_name || ''} (${user.email})`.trim(),
              type: 'user',
              first_name: user.first_name || '',
              last_name: user.last_name || '',
              middle_name: user.middle_name || '',
              email: user.email,
              variant: 'single',
              isLeaf: true,
            }
            acc[userId] = userNode
          }
          return acc
        },
        {} as Record<string, UserNode>,
      )

      dispatch(
        departmentsTreeSlice.actions.setTree({
          checkedUsers: checkedUsersObj,
          checkedKeys: checkedEmployees.reduce(
            (acc, userId) => {
              acc[userId] = true
              return acc
            },
            {} as Record<string, boolean>,
          ),
        }),
      )
    }

    return () => setUseInCourseAssignment(false)
  }, [
    useInCourseAssignment,
    isAllSelected,
    checkedEmployees,
    excludeEmployees,
    getCountOfEmployess,
    form,
    dispatch,
    setUseInCourseAssignment,
    employees,
  ])

  const breadcrumbItems = useMemo(
    () => [
      {
        id: URLS.ADMIN_LEARNING_PAGE,
        text: t('breadcrumbs.assigned_courses'),
        clickable: true,
      },
      {
        id: URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PAGE,
        text: t('breadcrumbs.assigning'),
        clickable: true,
      },
      {
        id: URLS.ADMIN_CREATE_COURSE_PAGE,
        text: t('breadcrumbs.assign_course'),
        clickable: false,
      },
    ],
    [t],
  )

  const isEmptyCoursesSelected = useMemo(
    () => selectedCourses.some(selectedCourse => selectedCourse?.themes_count === 0),
    [selectedCourses],
  )

  const onSubmit: SubmitHandler<FormValues> = async data => {
    if (!data.date_end || !data.date_start) return

    if (isEmptyCoursesSelected) {
      add({
        id: uuid(),
        status: 'error',
        message: t('validation.no_themes'),
      })
      return
    }

    const shouldUseFilteredAssignment = useInCourseAssignment && (isAllSelected || hasFilter)

    if (data?.courses?.length > 1) {
      const assignCoursesData = {
        courses: data.courses ?? [],
        need_assigned_message: !!data.notifications?.assign,
        need_notify_message: !!data.notifications?.lagging,
        period: differenceInDays(startOfDay(data.date_end), startOfDay(data.date_start)),
        start_date: format(startOfDay(new Date(data.date_start)), DASH_DATE_FORMAT),
        targets: data.targets ?? {
          departments: [],
          exclude_users_ids: [],
          users: [],
        },
      }

      if (shouldUseFilteredAssignment) {
        await assignCourses({
          body: assignCoursesData,
          params: {
            need_all: isAllSelected,
            search: debouncedSearch || '',
            in_phishing: filter?.phishing?.length ? filter.phishing : undefined,
            phishing_events: filter?.phishingEvents?.length ? filter.phishingEvents : undefined,
            roles: filter?.role || undefined,
            departments: filter?.departments?.length ? filter.departments : undefined,
            risk_level_from: filter?.riskLevelMin,
            risk_level_to: filter?.riskLevelMax,
            tags: filter?.tags?.length ? filter.tags : undefined,
            in_course: filter?.courses?.length ? filter.courses : undefined,
            course_progress: filter?.courseProgress?.length ? filter.courseProgress : undefined,
            learning: filter?.learning?.length ? filter.learning : undefined,
            include_ids: isAllSelected ? [] : checkedEmployees,
            exclude_ids: isAllSelected ? excludeEmployees : [],
          },
        }).unwrap()
      } else {
        await assignCourses({
          body: assignCoursesData,
        }).unwrap()
      }

      dispatch(resetFullStateForm())
      form.reset()
      navigate(URLS.ADMIN_LEARNING_PAGE)
      add({
        message: t('success'),
        status: 'success',
        id: uuid(),
      })
      return
    }

    if (data?.courses?.length === 1) {
      const course = data?.courses?.[0]
      const assignCourseData = {
        ...course,
        need_assigned_message: !!data.notifications?.assign,
        need_notify_message: !!data.notifications?.lagging,
        period: differenceInDays(startOfDay(data.date_end), startOfDay(data.date_start)),
        start_date: format(startOfDay(new Date(data.date_start)), DASH_DATE_FORMAT),
        targets: data.targets ?? {
          departments: [],
          exclude_users_ids: [],
          users: [],
        },
      }

      let courseData

      if (shouldUseFilteredAssignment) {
        courseData = await assignCourse({
          body: assignCourseData,
          params: {
            need_all: isAllSelected,
            search: debouncedSearch || '',
            in_phishing: filter?.phishing?.length ? filter.phishing : undefined,
            phishing_events: filter?.phishingEvents?.length ? filter.phishingEvents : undefined,
            roles: filter?.role || undefined,
            departments: filter?.departments?.length ? filter.departments : undefined,
            risk_level_from: filter?.riskLevelMin,
            risk_level_to: filter?.riskLevelMax,
            tags: filter?.tags?.length ? filter.tags : undefined,
            in_course: filter?.courses?.length ? filter.courses : undefined,
            course_progress: filter?.courseProgress?.length ? filter.courseProgress : undefined,
            learning: filter?.learning?.length ? filter.learning : undefined,
            include_ids: isAllSelected ? [] : checkedEmployees,
            exclude_ids: isAllSelected ? excludeEmployees : [],
          },
        }).unwrap()
      } else {
        courseData = await assignCourse({
          body: assignCourseData,
        }).unwrap()
      }

      if (data.image && data.image instanceof File)
        attachPicture({ assigned_course_id: courseData?.id, image: data.image })
      dispatch(resetFullStateForm())
      form.reset()
      navigate(URLS.ADMIN_LEARNING_PAGE)
      add({
        message: t('success'),
        status: 'success',
        id: uuid(),
      })
      return
    }
  }

  const targets = form.watch('targets')
  const storedTargets = useAppSelector(selectTargets)

  useEffect(() => {
    console.log(targets, 'targets')
  }, [targets])

  // Restore targets from store when component mounts (e.g., returning from course selection)
  useEffect(() => {
    if (
      !useInCourseAssignment &&
      storedTargets &&
      (storedTargets.users.length > 0 ||
        storedTargets.departments.length > 0 ||
        storedTargets.isAllSelected)
    ) {
      // Restore form values
      form.setValue(
        'targets',
        {
          users: storedTargets.users,
          departments: storedTargets.departments,
          exclude_users_ids: storedTargets.exclude_users_ids,
        },
        { shouldValidate: true },
      )

      // Restore organization tree modal state
      if (storedTargets.isAllSelected) {
        dispatch(departmentsTreeSlice.actions.setSelectAll(true))
      } else if (storedTargets.users.length > 0 && employees?.data) {
        // Create checkedUsers object for the tree state using actual user data
        const checkedUsersObj = storedTargets.users.reduce(
          (acc, userId) => {
            const user = employees.data.find(u => u.id === userId)
            if (user) {
              // Create a UserNode object
              const userNode: UserNode = {
                id: user.id,
                key: user.id,
                title: `${user.first_name || ''} ${user.last_name || ''} (${user.email})`.trim(),
                type: 'user',
                first_name: user.first_name || '',
                last_name: user.last_name || '',
                middle_name: user.middle_name || '',
                email: user.email,
                variant: 'single',
                isLeaf: true,
              }
              acc[userId] = userNode
            }
            return acc
          },
          {} as Record<string, UserNode>,
        )

        // Set the checked users in the tree
        dispatch(
          departmentsTreeSlice.actions.setTree({
            checkedUsers: checkedUsersObj,
            checkedKeys: storedTargets.users.reduce(
              (acc, userId) => {
                acc[userId] = true
                return acc
              },
              {} as Record<string, boolean>,
            ),
          }),
        )
      }
    }
  }, [storedTargets, employees, form, dispatch, useInCourseAssignment])

  return (
    <div className={cx('page')}>
      <div className={cx('header')}>
        <Breadcrumbs items={breadcrumbItems} className={cx('breadcrumbs')} />
        <h1 className={cx('title')}>{t('title')}</h1>
      </div>
      <form onSubmit={form.handleSubmit(onSubmit)} className={cx('form')}>
        <div className={cx('form__content')}>
          <div className={cx('choose')}>
            <h3 className={cx('title_small')}>{t('form.choose_course')}</h3>
            {selectedCourses?.length > 0 && (
              <Controller
                name='courses'
                control={form.control}
                render={({ fieldState }) => (
                  <>
                    <ul className={cx('courses', 'scrollbar')}>
                      {selectedCourses.map(course => (
                        <CourseCard
                          key={course.id}
                          course={course}
                          content={
                            course?.id === editingCourse ? (
                              <div className={cx('courses__item__edit')}>
                                <Input
                                  fullWidth
                                  value={course.title}
                                  onChange={v =>
                                    dispatch(
                                      onChangeSelectedCourseById({
                                        id: course.id,
                                        course: { ...course, title: v },
                                      }),
                                    )
                                  }
                                  label={t('form.editing.title')}
                                />
                                <Textarea
                                  className={cx('text-area')}
                                  fullWidth
                                  value={course.description ?? ''}
                                  onChange={v =>
                                    dispatch(
                                      onChangeSelectedCourseById({
                                        id: course.id,
                                        course: { ...course, description: v },
                                      }),
                                    )
                                  }
                                  label={t('form.editing.description')}
                                />
                              </div>
                            ) : null
                          }
                          wrapperClassName={
                            course?.id === editingCourse
                              ? cx('courses__item__edit__wrapper')
                              : undefined
                          }
                          contentClassName={cx('courses__item__content')}
                          endActions={
                            <div className={cx('courses__actions')}>
                              <IconWrapper
                                onClick={() =>
                                  setEditingCourse(
                                    course?.id === editingCourse ? undefined : course?.id,
                                  )
                                }
                                className={cx('courses__actions__item')}
                                color={course?.id === editingCourse ? 'gray90' : 'gray70'}
                              >
                                <EditBoldIcon />
                              </IconWrapper>
                              <IconWrapper
                                onClick={() => {
                                  setEditingCourse(undefined)
                                  dispatch(onDeleteFromSelectedCourses(course?.id))
                                }}
                                className={cx('courses__actions__item')}
                                color='gray70'
                              >
                                <TrashBoldIcon />
                              </IconWrapper>
                              {course?.themes_count === 0 && (
                                <Tooltip
                                  content={t('tooltip.no_themes')}
                                  tooltipClassname={cx('noThemesTooltip')}
                                >
                                  <IconWrapper className={cx('courses__actions__item')} color='red'>
                                    <ArticleWarningIcon />
                                  </IconWrapper>
                                </Tooltip>
                              )}
                            </div>
                          }
                        />
                      ))}
                    </ul>
                    {fieldState.error?.message}
                  </>
                )}
              />
            )}
            <div>
              <Button
                onClick={() => navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PAGE)}
                type='button'
                size='small'
                color={form.formState.errors.courses?.message ? 'red' : 'darkGray'}
              >
                <IconWrapper
                  size='16'
                  color={form.formState.errors.courses?.message ? 'white' : 'gray70'}
                >
                  <PlusIcon />
                </IconWrapper>
                <span>{t('form.add_course')}</span>
              </Button>
              {form.formState.errors?.courses?.message && (
                <p className='error-text'>{form.formState.errors?.courses?.message}</p>
              )}
            </div>
          </div>

          <div className={cx('notifications')}>
            <h3 className={cx('title_small')}>{t('form.notifications.title')}</h3>
            <Controller
              name='notifications'
              control={form.control}
              render={({ field }) => (
                <ul className={cx('notifications__list')}>
                  <li>
                    <Checkbox
                      customChecked={!!field.value?.['assign']}
                      onChange={v => {
                        field.onChange({ ...field.value, assign: v })
                        dispatch(setForm({ notifications: { ...field.value, assign: v } }))
                      }}
                      label={<p>{t('form.notifications.assign')}</p>}
                    />
                  </li>
                  <li>
                    <Checkbox
                      customChecked={!!field.value?.['lagging']}
                      onChange={v => {
                        field.onChange({ ...field.value, lagging: v })
                        dispatch(setForm({ notifications: { ...field.value, lagging: v } }))
                      }}
                      label={<p>{t('form.notifications.lagging')}</p>}
                    />
                  </li>
                </ul>
              )}
            />
            <Controller
              name='targets'
              control={form.control}
              render={({ field }) => (
                <SelectTargetCard
                  text={
                    isNumber(newCountOfEmployes) ? (
                      <div className={cx('targets__content')}>
                        {t(`commons:employees_choosed`, {
                          count: newCountOfEmployes,
                        })}
                        {isNumber(newCountOfEmployes) && (
                          <Tooltip
                            content={
                              <p className={cx('tooltip')}>
                                <span>{t('rules.1')}</span>
                                <span>{t('rules.2')}</span>
                                <span>{t('rules.3')}</span>
                              </p>
                            }
                          >
                            <IconWrapper size='20' color='red'>
                              <RiskLevelCircleIcon />
                            </IconWrapper>
                          </Tooltip>
                        )}
                      </div>
                    ) : (
                      t('select_targets')
                    )
                  }
                  active={isNumber(newCountOfEmployes)}
                  setOpen={v => dispatch(setTargetModalOpen(!!v))}
                  modalSlot={
                    targetModalOpen && (
                      <OrganizationTreeProvider
                        open={targetModalOpen}
                        setOpen={v => dispatch(setTargetModalOpen(!!v))}
                        handleSelect={state => {
                          dispatch(setSelectedCountOfPeople(state.countOfPeople))
                          dispatch(
                            setTargets({
                              isAllSelected: !!state?.selectAll,
                              users: !state?.selectAll ? state?.users_ids : [],
                              departments: state?.department_ids,
                              exclude_users_ids: state?.selectAll ? state?.users_ids : [],
                            }),
                          )
                          dispatch(
                            setForm({
                              targets: {
                                users: !state?.selectAll ? state?.users_ids : [],
                                departments: state?.department_ids,
                                exclude_users_ids: state?.selectAll ? state?.users_ids : [],
                              },
                            }),
                          )
                          field.onChange({
                            users: !state?.selectAll ? state?.users_ids : [],
                            departments: state?.department_ids,
                            exclude_users_ids: state?.selectAll ? state?.users_ids : [],
                          })
                        }}
                      >
                        <OrganizationTree />
                      </OrganizationTreeProvider>
                    )
                  }
                />
              )}
            />
            {form.formState.errors?.targets?.message && (
              <span className='error-text'>{form.formState.errors?.targets?.message}</span>
            )}
          </div>

          <div>
            <Controller
              name='date_start'
              control={form.control}
              render={({ field }) => (
                <SelectDateCardWithInput
                  label={t('learning.start')}
                  onChange={v => {
                    const date_start = v ?? null
                    field.onChange(date_start)
                    dispatch(
                      setForm({
                        date_start,
                      }),
                    )
                  }}
                  withoutTime
                  selected={field.value ?? null}
                  text={t('form.date_choose')}
                  min={startOfDay(new Date())}
                  max={
                    form.watch('date_end')
                      ? endOfDay(addDays(new Date(form.watch('date_end') as Date), -1))
                      : null
                  }
                />
              )}
            />
            <span className='error-text'>{form.formState.errors?.date_start?.message}</span>
          </div>
          <div>
            <Controller
              name='date_end'
              control={form.control}
              render={({ field }) => (
                <SelectDateCardWithInput
                  label={t('learning.end')}
                  onChange={v => {
                    const date_end = v ?? null
                    field.onChange(date_end)
                    dispatch(
                      setForm({
                        date_end,
                      }),
                    )
                  }}
                  withoutTime
                  selected={field.value ?? null}
                  text={t('form.date_choose')}
                  min={startOfDay(addDays(new Date(form.watch('date_start') ?? new Date()), 1))}
                />
              )}
            />
            <span className='error-text'>{form.formState.errors?.date_end?.message}</span>
          </div>
        </div>
        {selectedCourses?.length === 1 && (
          <div className={cx('form__image')}>
            <Controller
              control={form.control}
              name='image'
              render={({ field }) => <ImageUpload onSelect={field.onChange} />}
            />
          </div>
        )}
        <div className={cx('actions')}>
          <Button
            onClick={() => navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PAGE)}
            type='button'
            size='big'
            color='gray'
          >
            {t('actions.back')}
          </Button>
          <Button disabled={!form.formState.isValid || isLoading} type='submit' size='big'>
            {t('actions.assing')}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default Create
