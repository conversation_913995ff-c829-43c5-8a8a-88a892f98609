import {
  EntityState,
  createEntityAdapter,
  createSlice,
  PayloadAction,
  WithSlice,
  createSelector,
} from '@reduxjs/toolkit'
import { rootReducer } from '@/store/reducer'
import { FormValues } from './assign-course'
import {
  Course,
  CourseByIdResponse,
  CoursesSortBy,
  CoursesSortOrder,
  TargetsWithAllSelected,
} from '@/entities/courses'
import { TTheme } from '@/entities/themeCourse/model/types'
import { selectCurrentSelectedUsersCount } from '@/shared/modals/organization-tree'
import { RootState } from '@/store/store'

const selectedCoursesAdapter = createEntityAdapter<Course, Course['id']>({
  selectId: course => course.id,
})

type InitialState = {
  targetModalOpen: boolean
  targets: TargetsWithAllSelected
  selectedCourses: EntityState<Course, string>
  selectedCountOfPeople?: number
  courseSearch: string
  coursePage: number
  form: Partial<FormValues>
  sort: {
    by: CoursesSortBy
    order?: CoursesSortOrder
  }
  viewedCourse?: CourseByIdResponse
  viewedCourseTheme?: TTheme
  cameFromEmployeesPage: boolean
}

const DEFAULT_FORM = {
  date_start: null,
  date_end: null,
  targets: {
    users: [],
    departments: [],
    exclude_users_ids: [],
  },
  notifications: {
    lagging: false,
    assign: false,
  },
}

const initialState: InitialState = {
  targetModalOpen: false,
  courseSearch: '',
  targets: {
    users: [],
    departments: [],
    exclude_users_ids: [],
    isAllSelected: false,
  },
  coursePage: 1,
  selectedCourses: selectedCoursesAdapter.getInitialState(),
  form: DEFAULT_FORM,
  sort: {
    by: 'created_at',
  },
  cameFromEmployeesPage: false,
}

export const assignCourseSlice = createSlice({
  name: 'assignCourseSlice',
  initialState,
  reducers: {
    setSort: (state, action: PayloadAction<{ by: CoursesSortBy; order?: CoursesSortOrder }>) => {
      state.sort = {
        ...(state.sort ?? {}),
        ...action.payload,
      }
    },
    setTargetModalOpen: (state, action: PayloadAction<boolean>) => {
      state.targetModalOpen = action.payload
    },
    setSelectedCourses: (state, action: PayloadAction<Course[]>) => {
      state.selectedCourses = selectedCoursesAdapter.setAll(state.selectedCourses, action.payload)
    },
    handleSelectedCourses: (state, action: PayloadAction<Course>) => {
      if (state.selectedCourses.entities[action.payload.id]) {
        state.selectedCourses = selectedCoursesAdapter.removeOne(
          state.selectedCourses,
          action.payload.id,
        )
      } else {
        state.selectedCourses = selectedCoursesAdapter.addOne(state.selectedCourses, action.payload)
      }
    },
    handleSelectedCoursesPublic: (state, action: PayloadAction<Course>) => {
      if (state.selectedCourses.entities[action.payload.id]) {
        state.selectedCourses = selectedCoursesAdapter.removeOne(
          state.selectedCourses,
          action.payload.id,
        )
      } else {
        state.selectedCourses = selectedCoursesAdapter.setAll(state.selectedCourses, [
          action.payload,
        ])
      }
    },
    setSelectedCountOfPeople: (state, action: PayloadAction<number | undefined>) => {
      state.selectedCountOfPeople = action.payload
    },
    setCourseSearch: (state, action: PayloadAction<string>) => {
      state.courseSearch = action.payload
    },
    setCoursePage: (state, action: PayloadAction<number>) => {
      state.coursePage = action.payload
    },
    setTargets: (state, action: PayloadAction<TargetsWithAllSelected>) => {
      state.targets = action.payload
    },
    setCameFromEmployeesPage: (state, action: PayloadAction<boolean>) => {
      state.cameFromEmployeesPage = action.payload
    },
    setForm: (state, action: PayloadAction<Partial<FormValues>>) => {
      state.form = {
        ...state.form,
        ...action.payload,
      }
    },
    resetFullStateForm: state => {
      state.form = DEFAULT_FORM
      state.coursePage = 1
      state.selectedCourses = selectedCoursesAdapter.getInitialState()
      state.courseSearch = ''
      state.selectedCountOfPeople = undefined
      state.targetModalOpen = false
      state.targets = { departments: [], exclude_users_ids: [], isAllSelected: false, users: [] }
      state.cameFromEmployeesPage = false
    },
    onChangeSelectedCourseById: (
      state,
      action: PayloadAction<{ id: Course['id']; course: Partial<Course> }>,
    ) => {
      const selectedCourse = state.selectedCourses.entities[action.payload.id]
      state.selectedCourses = selectedCoursesAdapter.updateOne(state.selectedCourses, {
        id: action.payload.id,
        changes: {
          ...selectedCourse,
          ...action.payload.course,
        },
      })
    },
    onDeleteFromSelectedCourses: (state, action: PayloadAction<Course['id']>) => {
      state.selectedCourses = selectedCoursesAdapter.removeOne(
        state.selectedCourses,
        action.payload,
      )
    },
    setViewedCourse: (state, action: PayloadAction<CourseByIdResponse | undefined>) => {
      state.viewedCourse = action.payload
    },
    setViewedCourseTheme: (state, action: PayloadAction<TTheme | undefined>) => {
      state.viewedCourseTheme = action.payload
    },
  },
  selectors: {
    selectTargetModalOpen: state => state.targetModalOpen,
    selectSelectedCountOfPeople: state => state.selectedCountOfPeople,
    selectCourseSearch: state => state.courseSearch,
    selectSelectedCourses: state =>
      selectedCoursesAdapter.getSelectors().selectAll(state.selectedCourses),
    selectSelectedCoursesEntities: state => state.selectedCourses.entities,
    selectCoursePage: state => state.coursePage,
    selectTargets: state => state.targets,
    selectForm: state => state.form,
    selectSort: state => state.sort,
    selectViewedCourse: state => state.viewedCourse,
    selectViewedCourseTheme: state => state.viewedCourseTheme,
    selectCameFromEmployeesPage: state => state.cameFromEmployeesPage,
  },
})

declare module '@/store/reducer' {
  export interface LazyLoadedSlices extends WithSlice<typeof assignCourseSlice> {}
}

const injectedAssignCourseSlice = assignCourseSlice.injectInto(rootReducer)

export const {
  setTargetModalOpen,
  setSelectedCountOfPeople,
  setCourseSearch,
  handleSelectedCourses,
  handleSelectedCoursesPublic,
  setSelectedCourses,
  setCoursePage,
  setTargets,
  setCameFromEmployeesPage,
  setForm,
  resetFullStateForm,
  onChangeSelectedCourseById,
  onDeleteFromSelectedCourses,
  setSort,
  setViewedCourse,
  setViewedCourseTheme,
} = injectedAssignCourseSlice.actions
export const {
  selectTargetModalOpen,
  selectSelectedCountOfPeople,
  selectCourseSearch,
  selectSelectedCourses,
  selectSelectedCoursesEntities,
  selectCoursePage,
  selectTargets,
  selectForm,
  selectSort,
  selectViewedCourse,
  selectViewedCourseTheme,
  selectCameFromEmployeesPage,
} = injectedAssignCourseSlice.selectors

export const selectActualSelectedCountOfPeople = createSelector(
  [
    selectSelectedCountOfPeople,
    selectTargets,
    (state: RootState) => selectCurrentSelectedUsersCount(state),
  ],
  (staticCount, targets, dynamicCount) => {
    if (targets?.isAllSelected && dynamicCount !== undefined) {
      return dynamicCount
    }

    if (targets?.isAllSelected && dynamicCount === undefined && staticCount !== undefined) {
      return staticCount
    }

    return staticCount
  },
)
